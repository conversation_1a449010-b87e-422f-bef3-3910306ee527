import axios from 'axios'
import Env from '@ioc:Adonis/Core/Env'
import { v4 as uuidv4 } from 'uuid'
import { productProvider } from './ProductProvider'

interface MastraResponse {
  text?: string
  error?: string
  message?: string
  content?: string
}

interface ChatMessage {
  role: 'user' | 'assistant' | 'system'
  content: string
}

interface MastraRequestBody {
  messages: ChatMessage[]
  runId: string
  maxRetries: number
  maxSteps: number
  temperature: number
  topP: number
  runtimeContext: Record<string, any>
  threadId: string
  resourceId: string
}

interface QueryItem {
  q: string
  filters?: string
}

interface ParsedQuery {
  queries: QueryItem[]
}

function parseQueryContent(input: string): ParsedQuery | null {
  // Регулярное выражение для поиска содержимого между тегами <query> и </query>
  const queryRegex = /<query>\s*([\s\S]*?)\s*<\/query>/i
  const match = input.match(queryRegex)

  if (!match || !match[1]) {
    return null
  }

  try {
    // Парсим JSON содержимое
    const jsonContent = match[1].trim()
    const parsedData = JSON.parse(jsonContent)

    // Проверяем, что это массив
    if (!Array.isArray(parsedData)) {
      throw new Error('Query content must be an array')
    }

    // Валидируем каждый элемент массива
    const queries: QueryItem[] = parsedData.map((item, index) => {
      if (typeof item !== 'object' || item === null) {
        throw new Error(`Item at index ${index} must be an object`)
      }

      if (typeof item.q !== 'string') {
        throw new Error(`Item at index ${index} must have a 'q' property of type string`)
      }

      const queryItem: QueryItem = {
        q: item.q
      }

      // Добавляем filters если он существует и является строкой
      if (item.filters !== undefined) {
        if (typeof item.filters !== 'string') {
          throw new Error(`Item at index ${index} has 'filters' property that is not a string`)
        }
        queryItem.filters = item.filters
      }

      return queryItem
    })

    return {
      queries
    }
  } catch (error) {
    console.error('Ошибка при парсинге содержимого query:', error)
    return null
  }
}

class AiProvider {
  private mastraBaseUrl: string
  private agentId: string

  constructor() {
    this.mastraBaseUrl = Env.get('MASTRA_API_URL', 'http://localhost:4111/api')
    this.agentId = 'sealsAgent'
  }

  /**
   * Генерирует новый threadId для начала новой беседы
   */
  generateThreadId(): string {
    return uuidv4()
  }

  async aiChat({
    message,
    threadId,
    conversationHistory = [],
    temperature = 0.5,
    maxRetries = 2,
    maxSteps = 5,
    topP = 1
  }: {
    message: string
    threadId?: string
    conversationHistory?: ChatMessage[]
    temperature?: number
    maxRetries?: number
    maxSteps?: number
    topP?: number
  }) {
    try {
      // Если threadId не передан, генерируем новый
      const currentThreadId = threadId || this.generateThreadId()

      // Подготавливаем историю сообщений
      const messages: ChatMessage[] = [...conversationHistory, { role: 'user', content: message }]

      // Формируем тело запроса согласно API Mastra
      const requestBody: MastraRequestBody = {
        messages: messages,
        runId: this.agentId,
        maxRetries: maxRetries,
        maxSteps: maxSteps,
        temperature: temperature,
        topP: topP,
        runtimeContext: {},
        threadId: currentThreadId,
        resourceId: this.agentId
      }

      // Отправляем запрос к Mastra AI (используем /generate endpoint)
      const response = await axios.post(`${this.mastraBaseUrl}/agents/${this.agentId}/generate`, requestBody, {
        headers: {
          'Content-Type': 'application/json',
          'Accept': '*/*'
          // Добавьте заголовки авторизации если необходимо
          // 'Authorization': `Bearer ${Env.get('MASTRA_API_KEY')}`
        },
        timeout: 120000 // 120 секунд таймаут для генерации
      })

      const mastraResponse: MastraResponse = response.data
      const responseText = mastraResponse.text || mastraResponse.content || mastraResponse.message || 'Получен пустой ответ от AI'

      const productSearchParams = parseQueryContent(responseText)

      let products: [] | undefined = undefined

      if (productSearchParams?.queries) {
        products = await productProvider.searchFromAiChat(productSearchParams?.queries)
      }

      return {
        success: true,
        response: responseText,
        products,
        threadId: currentThreadId, // Возвращаем threadId клиенту
        agentId: this.agentId,
        conversationHistory: [...messages, { role: 'assistant', content: responseText }]
        // metadata: {
        //   temperature,
        //   maxRetries,
        //   maxSteps,
        //   topP
        // }
      }
    } catch (error) {
      console.error('Ошибка при обращении к Mastra AI:', error)

      // Обработка различных типов ошибок
      if (axios.isAxiosError(error)) {
        if (error.code === 'ECONNREFUSED') {
          return {
            success: false,
            response: 'Сервис AI временно недоступен. Попробуйте позже.',
            error: 'CONNECTION_REFUSED',
            threadId: threadId || this.generateThreadId()
          }
        }

        if (error.response?.status === 404) {
          return {
            success: false,
            response: 'AI агент не найден. Проверьте конфигурацию.',
            error: 'AGENT_NOT_FOUND',
            threadId: threadId || this.generateThreadId()
          }
        }

        if (error.response?.status === 429) {
          return {
            success: false,
            response: 'Превышен лимит запросов. Попробуйте позже.',
            error: 'RATE_LIMIT_EXCEEDED',
            threadId: threadId || this.generateThreadId()
          }
        }

        if (error.response?.status === 500) {
          return {
            success: false,
            response: 'Внутренняя ошибка сервера AI. Попробуйте позже.',
            error: 'INTERNAL_SERVER_ERROR',
            threadId: threadId || this.generateThreadId()
          }
        }
      }

      // Возвращаем fallback ответ
      return {
        success: false,
        response: `Извините, произошла ошибка при обработке вашего сообщения: "${message}". Попробуйте еще раз.`,
        error: 'UNKNOWN_ERROR',
        threadId: threadId || this.generateThreadId()
      }
    }
  }

  /**
   * Получает историю беседы по threadId
   */
  async getThreadHistory(threadId: string) {
    try {
      const response = await axios.get(`${this.mastraBaseUrl}/agents/${this.agentId}/threads/${threadId}`)
      return {
        success: true,
        threadId: threadId,
        history: response.data
      }
    } catch (error) {
      console.error('Ошибка при получении истории беседы:', error)
      return {
        success: false,
        error: 'Не удалось получить историю беседы',
        threadId: threadId
      }
    }
  }

  /**
   * Удаляет историю беседы по threadId
   */
  async deleteThread(threadId: string) {
    try {
      const response = await axios.delete(`${this.mastraBaseUrl}/agents/${this.agentId}/threads/${threadId}`)
      return {
        success: true,
        threadId: threadId,
        message: 'История беседы удалена'
      }
    } catch (error) {
      console.error('Ошибка при удалении истории беседы:', error)
      return {
        success: false,
        error: 'Не удалось удалить историю беседы',
        threadId: threadId
      }
    }
  }

  // Метод для получения информации об агенте
  async getAgentInfo() {
    try {
      const response = await axios.get(`${this.mastraBaseUrl}/agents/${this.agentId}`)
      return {
        success: true,
        agent: response.data
      }
    } catch (error) {
      console.error('Ошибка при получении информации об агенте:', error)
      return {
        success: false,
        error: 'Не удалось получить информацию об агенте'
      }
    }
  }

  // Метод для проверки здоровья Mastra API
  async healthCheck() {
    try {
      const response = await axios.get(`${this.mastraBaseUrl}/health`, { timeout: 5000 })
      return {
        success: true,
        status: response.data
      }
    } catch (error) {
      return {
        success: false,
        error: 'Mastra API недоступен'
      }
    }
  }
}

export const aiProvider = new AiProvider()
