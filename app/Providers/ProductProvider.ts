import { products, cat_columns, filters, cats, Prisma } from '@prisma/client'
import { isSize } from 'App/Helpers/isSize'
import { searchValueHandler } from 'App/Helpers/searchValueHandler'
import Product from 'App/Models/Product'
import { MeiliSearchPlugin } from '../Plugins/MeiliSearch'
import { $prisma } from 'App/Services/Prisma'
import { MeiliSearch } from 'meilisearch'
import { PageNumberCounters, PageNumberPagination } from 'prisma-extension-pagination/dist/types'
import { z } from 'zod'
import { getModelKeys } from 'App/Helpers/getModelColumns'
import sanitizeHtml from 'sanitize-html'
import Statistic from 'App/Models/Statistic'
import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Env from '@ioc:Adonis/Core/Env'

const SEARCH_FIELDS = ['prod_sku', 'prod_analogsku', 'prod_manuf', 'prod_note', 'prod_year', 'prod_type', 'prod_uses', 'prod_purpose', 'prod_analogs', 'prod_model']

const FULLTEXT_SEARCH_FIELDS = [
  'prod_analogsku',
  'prod_sku',
  'prod_manuf',
  'prod_note',
  'prod_model',
  'prod_type',
  'prod_uses',
  'prod_purpose',
  'prod_analogs',
  'prod_material',
  'prod_secret',
  'prod_supplier'
]

export const defaultAttributesToRetrieve = [
  'prod_id',
  'prod_sku',
  'prod_analogsku',
  'prod_cat',
  // 'prod_weight',
  'prod_price',
  'prod_count',
  'prod_manuf',
  'prod_note',
  'prod_year',
  'prod_type',
  'prod_uses',
  'prod_size',
  'prod_discount',
  'prod_purpose',
  'prod_analogs',
  'prod_material',
  'prod_rk',
  'prod_group',
  'prod_group_count',
  'prod_img',
  'prod_img_rumi',
  'prod_images',
  'prod_model'
]

export const SIZE_TOLERANCE = 0.3
export const SIZE_HEIGHT_TOLERANCE = 1

const GLOBAL_CATEGORY_ID = 9999

export type ProductWithMeta = {
  data: products[]
  meta: PageNumberPagination & PageNumberCounters
}

// Interfaces
interface Size {
  sizeIn: number
  sizeOut: number
  sizeHeight: number
  sizeIn_2?: number
  sizeOut_2?: number
  sizeHeight_2?: number
}

interface CategoryData {
  categoryId: string
  categoryTitle: string
  columns: cat_columns[]
  products: Product[]
  filters?: filters[]
}

interface SortingType {
  column: 'prod_sku' | string
  direction: 'asc' | 'desc'
  order: 1 | 0
}

export const ProductPaginateParamsSchema = z.object({
  page: z.number().optional(),
  limit: z.number().optional(),
  sortBy: z.string().optional(),
  order: z.string().optional(),
  sorting: z
    .array(
      z.object({
        column: z.string(),
        direction: z.string(),
        order: z.number().optional()
      })
    )
    .optional(),
  filters: z.any().optional()
})

export const FindByCategoryIdParamsSchema = z.object({
  identifier: z.string().or(z.number()),
  search: z.string().max(150).optional(),
  ...ProductPaginateParamsSchema.shape
})

export const GlobalSearchParamsSchema = z.object({
  searchvalue: z.string().max(150).optional(),
  ...ProductPaginateParamsSchema.shape
})

export type ProductPaginateParams = z.infer<typeof ProductPaginateParamsSchema>
export type FindByCategoryIdParams = z.infer<typeof FindByCategoryIdParamsSchema>
export type GlobalSearchParams = z.infer<typeof GlobalSearchParamsSchema>

const { clientProductDBfields } = Product.getProductColumns()
const searchFields = ['prod_sku', 'prod_analogsku', 'prod_manuf', 'prod_note', 'prod_year', 'prod_type', 'prod_uses', 'prod_purpose', 'prod_analogs', 'prod_model']
const fulltextSearchFields = [
  'prod_analogsku',
  'prod_sku',
  'prod_manuf',
  'prod_note',
  'prod_model',
  'prod_type',
  'prod_uses',
  'prod_purpose',
  'prod_analogs',
  'prod_material',
  'prod_secret',
  'prod_supplier'
]

const fields = [
  {
    keyname: 'prod_sku',
    title: 'Артикул'
  },
  {
    keyname: 'prod_analogsku',
    title: 'Аналог'
  },
  {
    keyname: 'prod_size',
    title: 'Размер',
    searchable: true
  },
  {
    keyname: 'prod_manuf',
    title: 'Производитель',
    searchable: true
  },
  {
    keyname: 'prod_year',
    title: 'Год'
  },
  {
    keyname: 'prod_type',
    title: 'Тип',
    searchable: true
  },
  {
    keyname: 'prod_material',
    title: 'Материал'
    // searchable: true
  },
  {
    keyname: 'prod_uses',
    title: 'Применение'
    // searchable: true
  },
  {
    keyname: 'prod_analogs',
    title: 'Аналоги',
    type: 'array'
  },
  {
    keyname: 'prod_model',
    title: 'Модель'
    // searchable: true
  },
  {
    keyname: 'prod_note',
    title: 'Описание',
    type: 'html'
  }
]

type FindByCategoryIdParamsReponse = {
  products: {
    data: Product[]
    meta: PageNumberPagination & PageNumberCounters
  }
  category: {
    columns: cat_columns[]
    filters: filters[]
    [key in cats]: string
  }
}

export class ProductProvider {
  db = $prisma.products
  catDB = $prisma.cats
  schemaDB = $prisma.product_schemas
  meiliDB: MeiliSearch

  constructor() {
    this.meiliDB = new MeiliSearch({
      host: 'http://localhost:7700',
      apiKey: 'aSampleMasterKey'
    })
  }

  getPublicFieldsObject() {
    const { clientProductDBfields } = Product.getProductColumns()
    const obj = {}

    clientProductDBfields.forEach((item) => {
      obj[item.split('.')[1]] = true
    })

    return obj
  }

  transformToAdonisModel(rawProducts: Partial<products>[], { cropFields = false, sanitizeHTML = false } = {}) {
    const modelKeys = getModelKeys(Product)

    return rawProducts.map((item) => {
      if (cropFields) {
        for (const key of Object.keys(item)) {
          if (!modelKeys.includes(key)) {
            delete item[key]
          }
        }
      }
      if (sanitizeHTML && item.prod_note) {
        item.prod_note = sanitizeHtml(item.prod_note)
      }
      const productOverModel = new Product()
      productOverModel.fill(item)

      return productOverModel
    })
  }

  private async initProductsWithMeili(products: Product[]) {
    const ts = Date.now()
    // //console.time('initProductsWithMeili: ' + ts)

    await Promise.all(
      products.map(async (product) => {
        if (!product.qty) {
          product.qty = 0
        }

        product.whosaleprice = Number((product.prod_price - (product.prod_price / 100) * product.prod_discount).toFixed(2))

        if (product.prod_group) {
          product.prod_count = product.prod_group_count
        }

        if (product.prod_rk) {
          try {
            const rkData = await this.meiliDB.index('products').search('', {
              filter: [
                `prod_id IN [${product.prod_rk
                  .split(',')
                  .filter((i) => i)
                  .map((i) => i.split('*')?.[0])
                  ?.filter((i) => i)}]`
              ],
              limit: 100
            })

            if (rkData.hits?.length) {
              const stocklist = rkData.hits.map((rkItem) => {
                const lst = product.prod_rk
                  ?.split(',')
                  .find((x) => x.split('*')[0] === String(rkItem.prod_id))
                  ?.split('*')

                if (lst?.[1]) {
                  return Math.floor(rkItem.prod_count / Number(lst[1]))
                }
                return 0
              })

              if (stocklist?.length) {
                product.prod_count = Math.min(...stocklist)
              } else {
                product.prod_count = 0
              }
            }
          } catch (error) {
            product.prod_count = 0
            console.error('getRkStock Meili error:', error)
          }
        }
      })
    )

    // //console.timeEnd('initProductsWithMeili: ' + ts)

    return products
  }

  async initAndTransformToAdonisModel(rawProducts: Partial<products>[], { cropFields = false, sanitizeHTML = false } = {}) {
    // Проверяем наличие prod_count у каждого товара
    // for (const product of rawProducts) {
    //   if (product.prod_count === undefined) {
    //     console.warn('⚠️ Missing prod_count for product:', product.prod_id, product.prod_sku)
    //     product.prod_count = 0
    //   }
    // }

    const adonisProducts = this.transformToAdonisModel(rawProducts, { cropFields })
    try {
      await this.initProductsWithMeili(adonisProducts)
    } catch (error) {
      console.error('⚠️ MeiliSearch transform failed, falling back to standard init:', error)
      await Product.productInit(adonisProducts)
    }

    // for (const product of adonisProducts) {
    //   if (product.prod_count === undefined) {
    //     console.warn('⚠️ Still missing prod_count after init for product:', product.prod_id, product.prod_sku)
    //     product.prod_count = 0
    //   }
    // }

    return adonisProducts
  }

  async getRkProducts({ prod_id }: { prod_id: number }) {
    const mainProduct = await this.db.findFirstOrThrow({
      where: {
        prod_id
      }
    })

    if (!mainProduct.prod_rk) {
      throw new Error('bad request: rk not found')
    }

    const rks = mainProduct.prod_rk
      .split(',')
      .filter(Boolean)
      .map((i) => Number(i.split('*')?.[0]))
      .filter((i) => Number.isNaN(i) === false)

    const products = await this.db.findMany({
      select: {
        ...this.getPublicFieldsObject()
      },
      where: {
        prod_id: {
          in: rks
        }
      }
    })

    return await this.initAndTransformToAdonisModel(products, { cropFields: true })
  }

  async filterDataByIdentifierMeili({ identifier, search = null, filters = null }) {
    // //console.time('filterDataByIdentifierMeili: ' + identifier)
    const identifiers = Array.isArray(identifier) ? [...new Set(identifier)] : [identifier]

    const categories = await this.getCategoriesFromIdentifiersMeili(identifiers)

    const { filter, filterConditions } = this.buildMeiliFilter(filters)
    const _value = this.processSearchQuery(search || '')

    let filterList = await this.meiliDB.index('filters').search('', {
      filter: [`category_id IN [${[...categories.map((cat) => Number(cat.cat_id))]}]`, 'field != bysize'],
      limit: 300,
      distinct: 'field'
    })

    if (filterList.hits.length === 0) {
      filterList = await this.meiliDB.index('filters').search('', {
        filter: [`category_id IN [${GLOBAL_CATEGORY_ID}]`, 'field != bysize'],
        limit: 300,
        distinct: 'field'
      })
    }

    let values: {
      [key: string]: {
        values: string[]
        title: string
      }
    } = {}

    await Promise.all(
      filterList.hits.map(async (filter) => {
        const searchQuery = _value || ''
        const productsWithFilter = await this.meiliDB.index('products').search(searchQuery, {
          filter: [`prod_cat IN [${categories.map((cat) => String(cat.cat_id))}]`, ...Object.entries(filterConditions).map(([key, values]) => `${key} IN [${values}]`)],
          attributesToRetrieve: [filter.field],
          distinct: filter.field,
          sort: ['prod_count:desc', `${filter.field}:asc`],
          matchingStrategy: 'frequency',
          limit: 300
        })
        values[filter.field] = {
          values: productsWithFilter.hits.filter((i) => i[filter.field]).map((product) => product[filter.field]),
          title: filter.title
        }
      })
    )

    if (categories?.length > 1) {
      values = {
        prod_cat: {
          values: categories.filter((i) => i.cat_rootcat !== 0).map((i) => String(i.cat_id)),
          title: 'Категория'
        },
        ...values
      }
    }

    // //console.timeEnd('filterDataByIdentifierMeili: ' + identifier)
    return values
  }

  async filterDataByIdentifier({ identifier, search = null, filters = null }) {
    //console.time('filterDataByIdentifier: ' + identifier)
    const identifiers = Array.isArray(identifier) ? [...new Set(identifier)] : [identifier]
    const categories = await this.getCategoriesFromIdentifiers(identifiers)
    let searchWhere: Prisma.productsWhereInput | undefined
    let filtersWhere: Prisma.productsWhereInput | undefined

    if (search) {
      searchWhere = this.makeSearchWhere({ filters, input: search })
    }

    if (filters) {
      filters = this.parseFilters(filters)
      filtersWhere = this.makeFiltersWhere(filters)
    }

    const filterList = await $prisma.filters.findMany({
      where: {
        category_id: {
          in: [...categories.map((cat) => Number(cat.cat_id)), GLOBAL_CATEGORY_ID]
        },
        field: {
          not: 'bysize'
        }
      },
      distinct: 'field',
      orderBy: {
        id: 'desc'
      }
    })

    const values: {
      [key: string]: {
        values: string[]
        title: string
      }
    } = {}

    await Promise.all(
      filterList.map(async (filter) => {
        const res = await this.db.groupBy({
          take: 500,
          by: [filter.field],
          where: {
            AND: [
              {
                prod_cat: {
                  in: categories.map((cat) => String(cat.cat_id))
                }
              },
              searchWhere ?? {},
              filtersWhere ?? {}
            ]
          },
          orderBy: {
            _count: {
              [filter.field]: 'desc'
            }
          }
        })

        values[filter.field] = {
          values: res.map((i) => i[filter.field]),
          title: filter.title
        }
      })
    )

    //console.timeEnd('filterDataByIdentifier: ' + identifier)
    return values
  }

  async findCategoryByIdentifier(identifier: string | number) {
    try {
      const category = await this.findCategoryByIdentifierMeili(identifier)

      if (category?.cat_id) {
        return category
      }
    } catch (error) {
      console.warn('@try findCategoryByIdentifier with meili:', error)
    }

    const category = await this.catDB.findFirst({
      where: {
        OR: [
          {
            cat_url_de: String(identifier)
          },
          {
            cat_url_en: String(identifier)
          },
          {
            cat_url_es: String(identifier)
          },
          {
            cat_url_fr: String(identifier)
          },
          {
            cat_url_it: String(identifier)
          },
          {
            cat_url_pl: String(identifier)
          },
          {
            cat_url_ru: String(identifier)
          },
          {
            cat_id: Number.isNaN(Number(identifier)) ? undefined : Number(identifier)
          }
        ],
        cat_active: true
      }
    })

    return category
  }

  async getRootCategories() {
    const timestamp = Date.now()

    //------ try query to meilisearch
    try {
      //console.time(`getRootCategories by meili: ${timestamp}`)
      const meiliCategories = await this.meiliDB.index('categories').search('', {
        filter: ['cat_rootcat = 0', 'cat_active = true'],
        limit: 100,
        sort: ['cat_sort:asc']
      })

      if (meiliCategories.hits?.length > 0) {
        //console.timeEnd(`getRootCategories by meili: ${timestamp}`)

        return meiliCategories.hits
      }
    } catch (error) {
      //console.log('@getRootCategories by MeiliSearch: ', error)
    }
    //------
    //console.time(`getRootCategories: ${timestamp}`)

    const categories = await this.catDB.findMany({
      where: {
        cat_rootcat: 0,
        cat_active: true
      },
      orderBy: {
        cat_sort: 'asc'
      }
    })
    //console.timeEnd(`getRootCategories: ${timestamp}`)

    return categories
  }

  async getCategories() {
    const timestamp = Date.now()
    //console.time(`getCategories: ${timestamp}`)
    type Category = cats & { subcategories?: cats[] }

    let categories: Category[] = []

    //------ try query to meilisearch
    try {
      //console.time(`getCategories by meili: ${timestamp}`)
      const meiliCategories = await this.meiliDB.index('categories').search('', {
        filter: ['cat_active = true'],
        limit: 100,
        sort: ['cat_sort:asc']
      })

      if (meiliCategories.hits?.length > 0) {
        //console.timeEnd(`getCategories by meili: ${timestamp}`)

        categories = meiliCategories.hits
      }
    } catch (error) {
      //console.log('@getRootCategories by MeiliSearch: ', error)
    }
    //------

    if (categories.length === 0) {
      categories = await this.catDB.findMany({
        where: {
          cat_active: true
        },
        orderBy: {
          cat_sort: 'desc'
        }
      })
    }

    for (const category of categories) {
      const subcategories = categories.filter((i) => i.cat_rootcat === category.cat_id)
      if (subcategories?.length) {
        category.subcategories = subcategories
      }
    }

    //console.timeEnd(`getCategories: ${timestamp}`)

    return categories.filter((i) => i.cat_rootcat == 0)
  }

  async getProductDataById(id: number) {
    const rawProduct = await this.db.findFirstOrThrow({
      where: {
        prod_id: id
      },
      select: this.getPublicFieldsObject()
    })

    const [product] = await this.initAndTransformToAdonisModel([rawProduct])
    return product
  }

  async getProductByIdMeili(id: number, request?: HttpContextContract['request']) {
    const ts = Date.now()
    console.time(`getProductByIdMeili: ${id} ${ts}`)

    const { clientProductDBfields } = Product.getProductColumns()

    let rawProduct

    try {
      const res = await this.meiliDB.index('products').search(null, {
        limit: 1,
        page: 1,
        attributesToRetrieve: clientProductDBfields.map((i) => i.split('.')[1]).filter(Boolean),
        filter: [`prod_id=${id}`]
      })

      rawProduct = res?.hits?.[0]
    } catch (error) {
      console.error(`getProductByIdMeili: ${error}, start fallback prisma query...`)

      rawProduct = await this.db.findFirstOrThrow({
        where: {
          prod_id: id
        },
        select: this.getPublicFieldsObject()
      })
    }

    if (!rawProduct) {
      throw new Error('404')
    }

    const [product] = await this.initAndTransformToAdonisModel([rawProduct])

    if (request) {
      Statistic.write({
        request,
        query: product.prod_analogsku,
        count: 1,
        manual: 0
      })
    }

    console.timeEnd(`getProductByIdMeili: ${id} ${ts}`)

    return { product, fields }
  }

  async getProductSchemaByProductId(id: number) {
    const ts = Date.now()
    console.time(`getProductSchemaByProductId: ${id} ${ts}`)

    const schemaRes = await this.schemaDB.findFirst({
      where: {
        OR: [
          { product_id: id },
          {
            related_ids: {
              contains: String(id)
            }
          }
        ]
      },
      select: {
        body: true
      }
    })

    let schema = null
    if (schemaRes) {
      try {
        if (schemaRes.body) {
          schema = JSON.parse(schemaRes.body)
        }
      } catch (error) {
        //console.log('🚀 ~ ProductProvider ~ getProductById schema parse ~ error:', error)
      }
    }

    console.time(`getProductSchemaByProductId: ${id} ${ts}`)
    return schema
  }
  async getProductById(id: number, request?: HttpContextContract['request']) {
    const ts = Date.now()
    console.time(`getProductById: ${id} ${ts}`)

    // Параллельный запуск двух независимых запросов
    const [rawProduct, schemaRes] = await Promise.all([
      this.db.findFirstOrThrow({
        where: {
          prod_id: id
        },
        select: this.getPublicFieldsObject()
      }),
      this.schemaDB.findFirst({
        where: {
          OR: [
            { product_id: id },
            {
              related_ids: {
                contains: String(id)
              }
            }
          ]
        },
        select: {
          body: true
        }
      })
    ])

    const [product] = await this.initAndTransformToAdonisModel([rawProduct])

    let schema = null
    if (schemaRes) {
      try {
        if (schemaRes.body) {
          schema = JSON.parse(schemaRes.body)
        }
      } catch (error) {
        //console.log('🚀 ~ ProductProvider ~ getProductById schema parse ~ error:', error)
      }
    }

    if (request) {
      Statistic.write({
        request,
        query: product.prod_analogsku,
        count: 1,
        manual: 0
      })
    }

    console.timeEnd(`getProductById: ${id} ${ts}`)

    return { product, fields, schema }
  }

  async getProductBreadcrumbs(id: number) {
    //console.time('getProductBreadcrumbs: ' + id)

    type BreadCrumb = {
      cat_id: Number
      cat_url_ru: String
      cat_title: String
      cat_rootcat?: Number
    }

    const buildCumulativePaths = (items) => {
      return items.map((item, index) => {
        const path = items
          .slice(0, index + 1)
          .map((i) => i.cat_url_ru)
          .join('/')
        return {
          ...item,
          fullPath: `/${path}`
        }
      })
    }

    const product = await this.db.findFirstOrThrow({
      where: {
        prod_id: id
      },
      select: {
        prod_cat: true
      }
    })

    const breadcrumbs: BreadCrumb[] = []

    const getCategoryChain = async (categoryId: number) => {
      const category = await this.catDB.findFirst({
        where: {
          cat_id: categoryId
        },
        select: {
          cat_id: true,
          cat_url_ru: true,
          cat_title: true,
          cat_rootcat: true
        }
      })

      if (category) {
        breadcrumbs.unshift({
          cat_id: category.cat_id,
          cat_url_ru: category.cat_url_ru,
          cat_title: category.cat_title
        })

        if (category.cat_rootcat) {
          await getCategoryChain(category.cat_rootcat)
        }
      }
    }

    await getCategoryChain(Number(product.prod_cat))
    //console.timeEnd('getProductBreadcrumbs: ' + id)
    return buildCumulativePaths(breadcrumbs)
  }

  async getCategoryNameById(id: number) {
    const category = await this.catDB.findFirst({
      where: {
        cat_id: id
      },
      select: {
        cat_title: true
      }
    })

    return category?.cat_title || undefined
  }

  async searchFromAiChat(queries: Array<{ q: string; filters?: string[] | string | undefined, sort?: string[] | string | undefined }>) {
    console.log('🚀 ~ ProductProvider ~ searchFromAiChat ~ queries:', queries)

    const res = await this.meiliDB.multiSearch({
      federation: { limit: 10 },
      queries: queries.map((i) => ({
        indexUid: 'products',
        q: i.q,
        filter: i.filters,
        sort: i.sort,
        // limit: 10,
        highlightPreTag: '_shl_',
        highlightPostTag: '_ehl_',
        matchingStrategy: 'frequency',
        attributesToRetrieve: defaultAttributesToRetrieve,
        attributesToHighlight: [
          'prod_sku',
          'prod_analogsku',
          'prod_manuf',
          'prod_note',
          'prod_year',
          'prod_type',
          'prod_uses',
          'prod_purpose',
          'prod_analogs',
          'prod_model',
          'prod_material'
        ]
      }))
    })

    return res.hits
    // return await this.transformToAdonisModel(res.hits)
  }

  // async globalFlatSearchMeili({ searchvalue = '', filters, limit = 100, page = 1, sorting = [] }: GlobalSearchParams) {
  //   const timestamp = Date.now()
  //   searchvalue = decodeURIComponent(searchvalue)
  //   //console.time(`globalFlatSearchMeili fetch: search/categories: | ${timestamp}`)

  //   // Обработка фильтров через отдельный метод
  //   const { filter } = this.buildMeiliFilter(filters)

  //   // Обработка сортировки через новый метод
  //   const sort = this.buildMeiliSortConditions(sorting)

  //   // Добавляем условия для размеров, если они присутствуют
  //   const sizeData = this.extractSizeData(searchvalue)
  //   if (sizeData) {
  //     filter.push(...this.getMeiliSizeFilters(sizeData))
  //     sort.push(...this.buildMeiliSortConditions([{ column: 'prod_size', direction: 'asc' }]))
  //   }

  //   //console.log('filter:', filter)

  //   // Обработка поискового запроса через вынесенный метод
  //   const _value = this.processSearchQuery(searchvalue)

  //   //console.log('_value:', _value)

  //   const [productsResults] = await Promise.all([
  //     this.meiliDB.index('products').search(_value, {
  //       offset: limit * (page - 1),
  //       limit,
  //       matchingStrategy: 'frequency',
  //       sort: [...sort, 'inStock:desc'],
  //       // sort,
  //       filter,
  //       attributesToRetrieve: defaultAttributesToRetrieve,
  //       attributesToHighlight: [
  //         'prod_sku',
  //         'prod_analogsku',
  //         'prod_manuf',
  //         'prod_note',
  //         'prod_year',
  //         'prod_type',
  //         'prod_uses',
  //         'prod_purpose',
  //         'prod_analogs',
  //         'prod_model',
  //         'prod_material'
  //       ],
  //       highlightPreTag: '__ais-highlight__',
  //       highlightPostTag: '__/ais-highlight__',
  //       attributesToCrop: ['prod_note', 'prod_uses'],
  //       cropLength: 100
  //       // attributesToSearchOn: [
  //       //   'prod_sku',
  //       //   'prod_analogsku',
  //       //   'prod_manuf',
  //       //   'prod_note',
  //       //   'prod_year',
  //       //   'prod_type',
  //       //   'prod_uses',
  //       //   'prod_purpose',
  //       //   'prod_analogs',
  //       //   'prod_model',
  //       //   'prod_material'
  //       // ]
  //     })
  //   ])
  //   //console.timeEnd(`globalFlatSearchMeili fetch: search/categories: | ${timestamp}`)

  //   const categoriesIds = Array.from(new Set(productsResults.hits.map((p) => Number(p.prod_cat))))
  //   const categoriesData: { [key: string]: any } = {}

  //   //console.time(`fetchMeiliCategoryData ${categoriesIds.join()} ${timestamp}`)
  //   await Promise.all(
  //     categoriesIds.map(async (catId) => {
  //       categoriesData[catId] = await this.fetchMeiliCategoryData(catId)
  //     })
  //   )
  //   //console.timeEnd(`fetchMeiliCategoryData ${categoriesIds.join()} ${timestamp}`)

  //   const totalCount = productsResults.totalHits || productsResults.estimatedTotalHits
  //   const flatProducts = productsResults.hits

  //   //console.time(`sanitizeHtml ${timestamp}`)

  //   for (const product of flatProducts) {
  //     if (product.prod_note?.length > 3) {
  //       product.prod_note = sanitizeHtml(product.prod_note, {
  //         allowedTags: ['b', 'p', 'li', 'ul', 'ol', 'br', 'hr']
  //       })
  //     }
  //   }

  //   //console.timeEnd(`sanitizeHtml ${timestamp}`)

  //   return {
  //     products: flatProducts,
  //     categoriesData,
  //     meta: {
  //       totalCount: totalCount,
  //       lastPage: Math.ceil(totalCount / limit),
  //       currentPage: page,
  //       perPage: limit,
  //       isFirstPage: page,
  //       isLastPage: page === Math.ceil(totalCount / limit),
  //       previousPage: page - 1,
  //       nextPage: page + 1,
  //       pageCount: Math.ceil(totalCount / limit)
  //     }
  //   }
  // }

  async globalSearchMeili(
    { searchvalue = '', filters, limit = 100, page = 1, sorting = [], groupByRoot = true }: GlobalSearchParams & { groupByRoot?: boolean },
    request: HttpContextContract['request']
  ) {
    searchvalue = decodeURIComponent(searchvalue)

    if (request) {
      Statistic.write({
        request,
        query: searchvalue,
        count: 1,
        manual: 0
      })
    }

    const timestamp = Date.now()
    console.time(`globalSearchMeili: ${searchvalue} | ${timestamp}`)

    // Используем объединённый метод для обработки фильтров
    const { filter } = this.buildMeiliFilter(filters)

    // Используем метод для построения условий сортировки

    // Добавляем условия для размеров, если они присутствуют
    const sizeData = this.extractSizeData(searchvalue)

    const sort = this.buildMeiliSortConditions(sorting, { ignoreSlashSize: !!sizeData })

    if (sizeData) {
      filter.push(...this.getMeiliSizeFilters(sizeData))
      sort.push(...this.buildMeiliSortConditions([{ column: 'prod_size', direction: 'asc' }], { ignoreSlashSize: true }))
      sort.push('cat_search_sort:asc')
    } else {
      sort.unshift('cat_search_sort:asc')
    }

    // Обработка поискового запроса через вынесенный метод
    const _value = this.processSearchQuery(searchvalue)

    // Формируем итоговый массив сортировок
    const finalSort = [...sort]

    finalSort.unshift('inStock:desc')

    const VAL = sizeData ? `${this.generateSizeString(sizeData)} ${_value}` : _value

    let productsResults = await this.meiliDB.index('products').search(VAL, {
      offset: limit * (page - 1),
      limit,
      filter,
      attributesToRetrieve: defaultAttributesToRetrieve,
      attributesToCrop: ['prod_note', 'prod_uses'],
      cropLength: 100,
      sort: finalSort,
      matchingStrategy: 'frequency',
      rankingScoreThreshold: sizeData && String(_value).trim().length === 0 ? 0.5 : VAL.split(' ').length < 2 ? 0.1 : undefined
    })

    if (!productsResults?.hits?.length) {
      console.log(`globalSearchMeili: ${VAL} not found by frequency matchingStrategy, try all strategy...`)

      productsResults = await this.meiliDB.index('products').search(VAL, {
        offset: limit * (page - 1),
        limit,
        filter,
        attributesToRetrieve: defaultAttributesToRetrieve,
        attributesToCrop: ['prod_note', 'prod_uses'],
        cropLength: 100,
        sort: finalSort,
        rankingScoreThreshold: 0.5
      })

      console.log(`globalSearchMeili: ${VAL} founded without frequency: ${productsResults?.hits?.length} items`)
    }

    // Сначала заменяем аналоги, сохраняя исходный порядок
    const productsWithReplacedAnalogs = this.replaceAnalogSkuWithFirstAnalog(productsResults.hits, _value)

    // Используем группировку по корневым категориям, сохраняя исходный порядок товаров
    // Получаем не только сгруппированные товары, но и порядок категорий
    const { productsByCategory, categoryOrder } = await this.groupProductsByRootCategory(productsWithReplacedAnalogs, groupByRoot)

    const categoriesData = await Promise.all(
      categoryOrder.map(async (rootCatId) => {
        const meiliData = await this.fetchMeiliCategoryData(rootCatId, { withAnalogColumn: true })
        const products = await this.initAndTransformToAdonisModel(productsByCategory[rootCatId], { cropFields: true })
        return {
          categoryId: rootCatId,
          categoryTitle: meiliData.category.cat_title,
          columns: meiliData.columns,
          products: products,
          filters: meiliData.filters
        }
      })
    )

    const totalCount = productsResults.totalHits || productsResults.estimatedTotalHits

    const res = {
      categories: categoriesData,
      meta: {
        totalCount: totalCount,
        lastPage: Math.ceil(totalCount / limit),
        currentPage: page,
        perPage: limit,
        isFirstPage: page,
        isLastPage: page === Math.ceil(totalCount / limit),
        previousPage: page - 1,
        nextPage: page + 1,
        pageCount: Math.ceil(totalCount / limit)
      },
      VAL
    }

    console.timeEnd(`globalSearchMeili: ${searchvalue} | ${timestamp}`)
    return res
  }

  async globalSearch(
    { searchvalue = '', filters, limit = 100, page = 1, sorting = [], groupByRoot = true }: GlobalSearchParams & { groupByRoot?: boolean },
    request?: HttpContextContract['request']
  ) {
    if (request) {
      Statistic.write({
        request,
        query: searchvalue,
        count: 1,
        manual: 0
      })
    }

    searchvalue = decodeURIComponent(searchvalue)
    //console.time(`globalSearch: ${searchvalue}`)
    let searchWhere: Prisma.productsWhereInput | undefined
    let filtersWhere: Prisma.productsWhereInput | undefined

    searchWhere = this.makeSearchWhere({ filters, input: searchvalue, enableFullTextSearch: false })
    if (filters) {
      filters = this.parseFilters(filters)
      filtersWhere = this.makeFiltersWhere(filters)
    }

    const orderBy = this.makeOrderBy(sorting)

    const activeCategories = await this.catDB.findMany({
      select: {
        cat_id: true
      },
      where: {
        cat_active: true
      }
    })

    const [rawProducts, meta] = await this.db
      .paginate({
        where: {
          AND: [
            !filters?.prod_cat
              ? {
                  prod_cat: {
                    in: activeCategories.map((i) => String(i.cat_id)).filter((i) => i)
                  }
                }
              : {},
            filtersWhere ?? {},
            searchWhere ?? {}
          ]
        },
        orderBy: [
          {
            _relevance: {
              fields: fulltextSearchFields,
              search: searchvalue,
              sort: 'asc'
            }
          },
          ...orderBy
        ],
        select: this.getPublicFieldsObject()
      })
      .withPages({
        includePageCount: true,
        limit: Number(limit),
        page: Number(page)
      })

    const { productsByCategory: productsByRootCategory } = await this.groupProductsByRootCategory(this.replaceAnalogSkuWithFirstAnalog(rawProducts, searchvalue), groupByRoot)

    const [tableFilters] = await Promise.all([
      $prisma.filters.findMany({
        where: {
          category_id: {
            in: rawProducts.map((i) => Number(i.prod_cat)).filter((i) => i)
          }
        }
      })
    ])

    const categoriesData = await Promise.all(
      Object.keys(productsByRootCategory).map(async (rootCatId) => {
        const [categoryColumns, categoryTitle] = await Promise.all([
          $prisma.cat_columns.findMany({
            where: {
              OR: [{ cat_id: Number(rootCatId) }, { cat_id: GLOBAL_CATEGORY_ID }]
            },
            orderBy: { sort: 'asc' },
            distinct: ['keyname']
          }),
          await this.getCategoryNameById(Number(rootCatId))
        ])

        const products = await this.initAndTransformToAdonisModel(productsByRootCategory[rootCatId])

        return {
          categoryId: rootCatId,
          categoryTitle: categoryTitle,
          columns: categoryColumns?.flat(),
          products: products
        }
      })
    )

    const res = {
      categories: categoriesData,
      meta: {
        ...meta
      },
      filters: tableFilters
    }

    //console.timeEnd(`globalSearch: ${searchvalue}`)
    return res
  }

  private makeFiltersWhere(filters) {
    try {
      if (typeof filters === 'string') {
        filters = JSON.parse(decodeURIComponent(filters))
      }
    } catch (error) {
      //console.log('Error parsing:', error)
    }

    Object.keys(filters).forEach((key) => !filters[key]?.length && delete filters[key])

    let filtersWhere: Prisma.productsWhereInput = {
      AND: Object.keys(filters).map((filter) => ({
        [filter]: { in: filters[filter] }
      }))
    }

    return filtersWhere
  }

  private makeSearchWhere({ input, filters, enableFullTextSearch = true }) {
    try {
      if (typeof filters === 'string') {
        filters = JSON.parse(decodeURIComponent(filters))
      }
    } catch (error) {
      //console.log('Error parsing filters in makeSearchWhere:', error)
    }

    let searchWhere: Prisma.productsWhereInput = {}
    const { _value } = searchValueHandler(
      {
        value: input,
        productDBfields: clientProductDBfields,
        sizeField: 'prod_size'
      },
      filters
    )

    const searchValue =
      _value
        ?.split(' ')
        .filter((i) => i)
        .map((i) => String(i).trim()) || []
    const sizeData = this.extractSizeData(input)
    if (sizeData) {
      searchWhere.AND = [
        {
          ...this.getPrismaSizeConditions(sizeData)
        }
      ]
    }

    if (enableFullTextSearch) {
      searchWhere.AND = [
        ...(searchWhere.AND || []),
        ...searchValue.map((val) => ({
          OR: fulltextSearchFields.map((field) => ({
            [field]: { search: val }
          }))
        }))
      ]
    } else {
      searchWhere.AND = [
        ...(searchWhere.AND || []),
        ...searchValue.map((val) => ({
          OR: searchFields.map((field) => ({
            [field]: { contains: val }
          }))
        }))
      ]
    }

    return searchWhere
  }

  async getSubCategories(identifier: number | string) {
    if (!identifier) throw new Error('ProductProvider. error: category identifier undefined')

    try {
      const category = await this.findCategoryByIdentifierMeili(identifier)

      if (category) {
        const subcategories = await this.meiliDB.index('categories').search('', {
          filter: [`cat_rootcat = ${category.cat_id} AND cat_active = true`],
          limit: 100
        })

        if (subcategories.hits.length > 0) {
          return subcategories.hits
        }
      }
    } catch (error) {
      console.error('@getSubCategories with meili', error)
    }

    const category = await this.findCategoryByIdentifier(identifier)
    if (!category) return null
    const subcategories = await this.catDB.findMany({
      where: {
        cat_rootcat: category.cat_id,
        cat_active: true
      }
    })
    if (!subcategories.length) return null
    return subcategories
  }

  async findByCategoryIdOrUrl({ identifier, page = 1, limit = 100, sorting = [], search, filters }: FindByCategoryIdParams) {
    //console.time(`query in catalog: ${identifier}`)

    if (!identifier) throw new Error('ProductProvider.findByCategoryIdOrUrl error: category identifier undefined')

    const category = await this.findCategoryByIdentifier(identifier)
    if (!category) throw new Error(`ProductProvider.findByCategoryIdOrUrl error: category "${identifier}" not found`)

    const sortByStockCats: string[] = String(Env.get('SORT_BY_STOCK_CATS')).split(',')

    const sortByStockEnabled = !!sortByStockCats.find((x) => x === String(category.cat_id))

    try {
      filters = JSON.parse(filters)
    } catch (error) {}

    let searchWhere: Prisma.productsWhereInput | undefined
    let filtersWhere: Prisma.productsWhereInput | undefined

    if (search) {
      searchWhere = this.makeSearchWhere({ filters, input: search })
    }

    if (filters) {
      filtersWhere = this.makeFiltersWhere(filters)
    }

    const orderBy = this.orderByFromArray(sorting)

    const [categoryFilters, categoryColumns] = await Promise.all([
      $prisma.filters.findMany({
        where: {
          category_id: {
            in: [Number(category.cat_id), GLOBAL_CATEGORY_ID]
          }
        },
        distinct: 'field',
        orderBy: { id: 'desc' }
      }),
      $prisma.cat_columns.findMany({
        where: {
          OR: [{ cat_id: Number(category.cat_id) }, { cat_id: GLOBAL_CATEGORY_ID }]
        },
        orderBy: {
          sort: 'asc'
        },
        distinct: ['keyname']
      })
    ])

    const [rawProducts, meta] = await this.db
      .paginate({
        where: {
          AND: [{ prod_cat: String(category.cat_id) }, searchWhere ?? {}, filtersWhere ?? {}]
        },
        orderBy,
        select: this.getPublicFieldsObject()
      })
      .withPages({
        includePageCount: true,
        limit: Number(limit),
        page: Number(page)
      })

    //console.time('transformToAdonisModel')
    const products = await this.initAndTransformToAdonisModel(rawProducts)
    //console.timeEnd('transformToAdonisModel')

    const res = {
      products: {
        data: products,
        meta
      },
      category: {
        columns: categoryColumns,
        filters: categoryFilters,
        ...category
      }
    }

    //console.timeEnd(`query in catalog: ${identifier}`)
    return res
  }

  /**
   * Находит категорию по идентификатору (ID или URL) с использованием MeiliSearch
   * @param identifier ID или URL категории
   * @returns Объект категории или null, если категория не найдена
   */
  async findCategoryByIdentifierMeili(identifier: string | number) {
    // Формируем фильтр для поиска категории по различным URL или ID
    const filters = [
      `cat_url_de = "${String(identifier)}"`,
      `cat_url_en = "${String(identifier)}"`,
      `cat_url_es = "${String(identifier)}"`,
      `cat_url_fr = "${String(identifier)}"`,
      `cat_url_it = "${String(identifier)}"`,
      `cat_url_pl = "${String(identifier)}"`,
      `cat_url_ru = "${String(identifier)}"`
    ]

    // Если идентификатор может быть числом, добавляем поиск по cat_id
    if (!Number.isNaN(Number(identifier))) {
      filters.push(`cat_id = ${Number(identifier)}`)
    }

    // Выполняем поиск в MeiliSearch
    const categorySearch = await this.meiliDB.index('categories').search('', {
      filter: [`(${filters.join(' OR ')}) AND cat_active = true`],
      limit: 1
    })

    // Возвращаем первую найденную категорию или null
    return categorySearch.hits.length > 0 ? categorySearch.hits[0] : null
  }

  async findByCategoryIdOrUrlWithMeili({ identifier, page = 1, limit = 100, sorting = [], filters }: FindByCategoryIdParams) {
    const RK_CATS: string[] = Env.get('RK_CATS') || []
    const RK_SORT_FIELD = Env.get('RK_SORT_FIELD') || 'prod_uses'

    let isRkCategory = false

    const timestamp = Date.now()
    //console.log(`@start findByCategoryIdOrUrlWithMeili: ${identifier}, page: ${page}`)

    //console.time(`query in catalog with MeiliSearch: ${identifier}`)

    if (!identifier) throw new Error('ProductProvider.findByCategoryIdOrUrlWithMeili error: category identifier undefined')

    //console.time(`findCategoryByIdentifierMeili ${identifier} ${timestamp}`)
    // Используем MeiliSearch для поиска категории11
    const category = await this.findCategoryByIdentifierMeili(identifier)
    if (!category) throw new Error(`ProductProvider.findByCategoryIdOrUrlWithMeili error: category "${identifier}" not found`)
    //console.timeEnd(`findCategoryByIdentifierMeili ${identifier} ${timestamp}`)

    try {
      isRkCategory = !!String(RK_CATS)
        .split(',')
        .find((i) => i === String(category.cat_id))
    } catch (error) {}

    //console.time(`read env ${timestamp}`)
    const sortByStockCats: string[] = String(Env.get('SORT_BY_STOCK_CATS')).split(',')
    const sortByStockEnabled = !!sortByStockCats.find((x) => x === String(category.cat_id))
    //console.timeEnd(`read env ${timestamp}`)

    try {
      filters = JSON.parse(filters)
    } catch (error) {}

    // Получаем данные категории из MeiliSearch
    //console.time(`fetchMeiliCategoryData ${category.cat_id} ${timestamp}`)

    const categoryData = await this.fetchMeiliCategoryData(category.cat_id)

    //console.timeEnd(`fetchMeiliCategoryData ${category.cat_id} ${timestamp}`)

    // Обработка фильтров через отдельный метод
    const { filter } = this.buildMeiliFilter(filters)

    // Добавляем фильтр по категории
    filter.push(`prod_cat = ${category.cat_id}`)

    // Обработка сортировки через метод для MeiliSearch
    const sort = this.buildMeiliSortConditions(sorting)

    sort.push(...this.getMeiliSizeSort('asc'))

    // Добавляем сортировку по наличию в конец, если это необходимо
    if (sortByStockEnabled) {
      sort.unshift('inStock:desc')
    }

    if (isRkCategory) {
      sort.unshift(`${RK_SORT_FIELD}:asc`)
    }

    //console.log('🚀 ~ ProductProvider ~ findByCategoryIdOrUrlWithMeili ~ sort:', sort)

    //console.time(`findByCategoryIdOrUrlWithMeili fetch: ${identifier}`)

    // Выполняем поиск в MeiliSearch

    const productsResults = await this.meiliDB.index('products').search(null, {
      offset: limit * (page - 1),
      limit,
      filter,
      attributesToRetrieve: defaultAttributesToRetrieve,
      attributesToCrop: ['prod_note', 'prod_uses'],
      cropLength: 100,
      sort,
      matchingStrategy: 'frequency'
    })

    //console.timeEnd(`findByCategoryIdOrUrlWithMeili fetch: ${identifier}`)

    // Преобразуем результаты поиска в модели Adonis
    //console.time('transformToAdonisModel')
    const products = await this.initAndTransformToAdonisModel(productsResults.hits, { cropFields: true })
    //console.timeEnd('transformToAdonisModel')

    // Формируем метаданные для пагинации
    const totalCount = productsResults.totalHits || productsResults.estimatedTotalHits
    const meta = {
      totalCount: totalCount,
      lastPage: Math.ceil(totalCount / limit),
      currentPage: page,
      perPage: limit,
      isFirstPage: page === 1,
      isLastPage: page === Math.ceil(totalCount / limit),
      previousPage: page > 1 ? page - 1 : null,
      nextPage: page < Math.ceil(totalCount / limit) ? page + 1 : null,
      pageCount: Math.ceil(totalCount / limit)
    }

    // Формируем результат
    const res = {
      products: {
        data: products,
        meta
      },
      category: {
        columns: categoryData.columns,
        filters: categoryData.filters,
        ...categoryData.category
      }
    }

    //console.timeEnd(`query in catalog with MeiliSearch: ${identifier}`)
    return res
  }

  async getProductsByIds({ ids, page = 1 }: { ids: number[]; page?: number }) {
    const [rawProducts, meta] = await this.db
      .paginate({
        where: {
          prod_id: {
            in: ids
          }
        },
        select: this.getPublicFieldsObject()
      })
      .withPages({
        includePageCount: true,
        limit: Number(200),
        page: Number(page)
      })

    const { productsByCategory: productsByRootCategory } = await this.groupProductsByRootCategory(rawProducts)

    const categoriesData = await Promise.all(
      Object.keys(productsByRootCategory).map(async (rootCatId) => {
        const [categoryColumns, categoryTitle] = await Promise.all([
          $prisma.cat_columns.findMany({
            where: {
              OR: [{ cat_id: Number(rootCatId) }, { cat_id: GLOBAL_CATEGORY_ID }]
            },
            orderBy: { sort: 'asc' },
            distinct: ['keyname']
          }),
          await this.getCategoryNameById(Number(rootCatId))
        ])

        const products = await this.initAndTransformToAdonisModel(productsByRootCategory[rootCatId])
        return {
          categoryId: rootCatId,
          categoryTitle: categoryTitle,
          columns: categoryColumns,
          products: products
        }
      })
    )

    return {
      categories: categoriesData,
      meta
    }
  }

  private orderByFromArray(sortArray: Array<{ column: string; direction: 'asc' | 'desc'; order: number }>) {
    // Создаем копию массива сортировки
    const sorting = [...sortArray]

    // Проверяем, есть ли сортировка по размеру или массив пуст
    const bySize = sorting?.some((i) => i.column === 'prod_size') || !sorting?.length

    if (bySize) {
      // Находим индекс элемента с сортировкой по размеру
      const bySizeIndex = sorting?.findIndex((i) => i.column === 'prod_size') ?? 0
      // Получаем направление сортировки
      const sizeOrderType = sorting[bySizeIndex]?.direction ?? 'asc'

      // Заменяем элемент с сортировкой по размеру на набор сортировок по отдельным полям размера
      sorting.splice(
        bySizeIndex,
        1,
        { column: 'size_in', direction: sizeOrderType, order: sorting[bySizeIndex]?.order ?? 0 },
        { column: 'size_in_2', direction: sizeOrderType, order: sorting[bySizeIndex]?.order ?? 0 },
        { column: 'size_out', direction: sizeOrderType, order: sorting[bySizeIndex]?.order ?? 0 },
        { column: 'size_out_2', direction: sizeOrderType, order: sorting[bySizeIndex]?.order ?? 0 },
        { column: 'size_h', direction: sizeOrderType, order: sorting[bySizeIndex]?.order ?? 0 },
        { column: 'size_h_2', direction: sizeOrderType, order: sorting[bySizeIndex]?.order ?? 0 }
      )
    }

    // Преобразуем массив объектов сортировки в формат, ожидаемый Prisma
    const orderBy = sorting.map((item) => ({
      [item.column]: item.direction
    }))

    return orderBy
  }

  private makeOrderBy(initValue: any[]) {
    const sorting = [...initValue]
    const bySize = sorting?.some((i) => Object.keys(i).some((key) => key === 'prod_size')) || !sorting?.length

    if (bySize) {
      const bySizeIndex = sorting?.findIndex((i) => Object.keys(i).some((key) => key === 'prod_size')) ?? 0
      const sizeOrderType = sorting[bySizeIndex]?.['prod_size'] ?? 'asc'

      sorting.splice(
        bySizeIndex,
        1,
        ...[
          { size_in: sizeOrderType },
          { size_in_2: sizeOrderType },
          { size_out: sizeOrderType },
          { size_out_2: sizeOrderType },
          { size_h: sizeOrderType },
          { size_h_2: sizeOrderType }
        ]
      )
    }

    return sorting
  }

  // ===================== ВСПОМОГАТЕЛЬНЫЕ МЕТОДЫ =====================

  private parseFilters(rawFilters: any): any {
    if (!rawFilters) return null
    try {
      if (typeof rawFilters === 'string') {
        return JSON.parse(decodeURIComponent(rawFilters))
      }
    } catch (error) {
      //console.log('Error parsing filters:', error)
    }
    return rawFilters
  }

  public generateSizeString(
    data: {
      sizeIn: number
      sizeIn_2?: number
      sizeOut: number
      sizeOut_2?: number
      sizeHeight?: number
      sizeHeight_2?: number
    } | null,
    delimiter = '*' // новый параметр с default значением
  ): string | null {
    if (!data) return null

    // Сборка частей с учетом дробей
    const buildPart = (main: number, fraction?: number) => (fraction !== undefined ? `${main}/${fraction}` : `${main}`)

    const parts = [buildPart(data.sizeIn, data.sizeIn_2), buildPart(data.sizeOut, data.sizeOut_2)]

    // Добавляем height если есть
    if (data.sizeHeight !== undefined) {
      parts.push(buildPart(data.sizeHeight, data.sizeHeight_2))
    }

    // Соединяем выбранным разделителем
    return parts.join(delimiter)
  }

  public extractSizeData(text: string): {
    sizeIn: number
    sizeIn_2?: number
    sizeOut: number
    sizeOut_2?: number
    sizeHeight?: number
    sizeHeight_2?: number
  } | null {
    const candidate = text.split(' ').find((word) => isSize(word))
    if (!candidate) return null
    const sizeregexp = /(\*|x|х|X|Х|\s|на|НА|-|:)/gm
    const normalized = candidate.replace(sizeregexp, '*')
    const sizes = normalized
      .split('*')
      .filter(Boolean)
      .map((size) => size.replace(/,/g, '.').replace(/\\/g, '/'))

    if (sizes.length < 2) return null

    let sizeIn = 0
    let sizeOut = 0
    let sizeHeight = 0
    let sizeIn_2: number | undefined
    let sizeOut_2: number | undefined
    let sizeHeight_2: number | undefined

    try {
      const parts0 = sizes[0].split('/')
      sizeIn = Number(parts0[0])
      sizeIn_2 = parts0[1] ? Number(parts0[1]) : undefined
      const parts1 = sizes[1].split('/')
      sizeOut = Number(parts1[0])
      sizeOut_2 = parts1[1] ? Number(parts1[1]) : undefined
    } catch (error) {
      sizeIn = Number(sizes[0])
      sizeOut = Number(sizes[1])
    }
    if (sizes[2]) {
      try {
        const parts2 = sizes[2].split('/')
        sizeHeight = Number(parts2[0])
        sizeHeight_2 = parts2[1] ? Number(parts2[1]) : undefined
      } catch (error) {
        sizeHeight = Number(sizes[2])
      }
    }
    return { sizeIn, sizeIn_2, sizeOut, sizeOut_2, sizeHeight: sizes[2] ? sizeHeight : undefined, sizeHeight_2 }
  }

  public getMeiliSizeFiltersExact(sizeData: {
    sizeIn: number // единственный обязательный параметр
    sizeIn_2?: number
    sizeOut?: number // делаем опциональным
    sizeOut_2?: number
    sizeHeight?: number
    sizeHeight_2?: number
  }): string[] {
    const filters: string[] = []

    // Обязательный параметр
    filters.push(`size_in = ${sizeData.sizeIn}`)

    // Опциональные параметры (добавляем только если они есть)
    if (sizeData.sizeIn_2) {
      filters.push(`size_in_2 = ${sizeData.sizeIn_2}`)
    }
    if (sizeData.sizeOut) {
      // теперь проверяем наличие
      filters.push(`size_out = ${sizeData.sizeOut}`)
    }
    if (sizeData.sizeOut_2) {
      filters.push(`size_out_2 = ${sizeData.sizeOut_2}`)
    }
    if (sizeData.sizeHeight) {
      filters.push(`size_h = ${sizeData.sizeHeight}`)
    }
    if (sizeData.sizeHeight_2) {
      filters.push(`size_h_2 = ${sizeData.sizeHeight_2}`)
    }

    return filters
  }

  public getMeiliSizeFilters(sizeData: { sizeIn: number; sizeIn_2?: number; sizeOut: number; sizeOut_2?: number; sizeHeight?: number; sizeHeight_2?: number }): string[] {
    const tolerance = SIZE_TOLERANCE
    const heiht_tolerance = SIZE_HEIGHT_TOLERANCE

    const filters: string[] = []
    filters.push(`size_in ${sizeData.sizeIn - tolerance} TO ${sizeData.sizeIn + tolerance}`)
    if (sizeData.sizeOut) {
      filters.push(`size_out ${sizeData.sizeOut - tolerance} TO ${sizeData.sizeOut + tolerance}`)
    }
    if (sizeData.sizeIn_2) {
      filters.push(`size_in_2 ${sizeData.sizeIn_2 - tolerance} TO ${sizeData.sizeIn_2 + tolerance}`)
    }
    if (sizeData.sizeOut_2) {
      filters.push(`size_out_2 ${sizeData.sizeOut_2 - tolerance} TO ${sizeData.sizeOut_2 + tolerance}`)
    }
    if (sizeData.sizeHeight) {
      filters.push(`size_h ${sizeData.sizeHeight - heiht_tolerance} TO ${sizeData.sizeHeight + heiht_tolerance}`)
    }
    if (sizeData.sizeHeight_2) {
      filters.push(`size_h_2 ${sizeData.sizeHeight_2 - heiht_tolerance} TO ${sizeData.sizeHeight_2 + heiht_tolerance}`)
    }
    return filters
  }

  private getPrismaSizeConditions(sizeData: {
    sizeIn: number
    sizeIn_2?: number
    sizeOut: number
    sizeOut_2?: number
    sizeHeight?: number
    sizeHeight_2?: number
  }): Prisma.productsWhereInput {
    const tolerance = SIZE_TOLERANCE
    const heiht_tolerance = SIZE_HEIGHT_TOLERANCE

    return {
      size_in: { gte: sizeData.sizeIn - tolerance, lte: sizeData.sizeIn + tolerance },
      size_in_2: sizeData.sizeIn_2 ? { gte: sizeData.sizeIn_2 - tolerance, lte: sizeData.sizeIn_2 + tolerance } : {},
      size_out: sizeData.sizeOut ? { gte: sizeData.sizeOut - tolerance, lte: sizeData.sizeOut + tolerance } : {},
      size_out_2: sizeData.sizeOut_2 ? { gte: sizeData.sizeOut_2 - tolerance, lte: sizeData.sizeOut_2 + tolerance } : {},
      size_h: sizeData.sizeHeight ? { gte: sizeData.sizeHeight - tolerance, lte: sizeData.sizeHeight + tolerance } : {},
      size_h_2: sizeData.sizeHeight_2 ? { gte: sizeData.sizeHeight_2 - heiht_tolerance, lte: sizeData.sizeHeight + heiht_tolerance } : {}
    }
  }

  private async getCategoriesFromIdentifiers(identifiers: (string | number)[]): Promise<cats[]> {
    const categories: cats[] = []
    await Promise.all(
      identifiers.map(async (id) => {
        const category = await this.findCategoryByIdentifier(id)
        if (category) categories.push(category)
      })
    )
    return categories
  }

  private async getCategoriesFromIdentifiersMeili(identifiers: (string | number)[]): Promise<cats[]> {
    //console.log('Called getCategoriesFromIdentifiersMeili with:', identifiers)
    const categories: cats[] = []
    const processedCatIds = new Set<number>()

    await Promise.all(
      identifiers.map(async (id) => {
        // Ищем основную категорию
        const categoryResult = await this.meiliDB.index('categories').search('', {
          filter: [`cat_id = ${id}`],
          limit: 1
        })

        if (categoryResult.hits.length === 0) return
        const mainCategory = categoryResult.hits[0]

        // Проверяем дубликаты
        if (processedCatIds.has(mainCategory.cat_id)) return
        processedCatIds.add(mainCategory.cat_id)
        categories.push(mainCategory)

        // Если это корневая категория, получаем всех потомков
        if (mainCategory.cat_rootcat === 0) {
          const descendants = await this.meiliDB.index('categories').search('', {
            filter: [`cat_rootcat = ${mainCategory.cat_id}`, 'cat_active = true'],
            limit: 1000
          })

          // Добавляем только новые категории
          for (const descendant of descendants.hits) {
            if (!processedCatIds.has(descendant.cat_id)) {
              processedCatIds.add(descendant.cat_id)
              categories.push(descendant)
            }
          }
        }
      })
    )

    return categories
  }

  private async getRootCategoryId(catId: string | number, cache?: Map<string, number>): Promise<number> {
    // Если передан кэш, проверяем наличие значения в нем
    if (cache && cache.has(String(catId))) {
      return cache.get(String(catId))!
    }

    let currentCatId = Number(catId)
    while (true) {
      const categoryData = await this.fetchMeiliCategoryData(currentCatId)
      if (!categoryData.category || categoryData.category.cat_rootcat === 0) {
        // Если передан кэш, сохраняем результат в нем
        if (cache) {
          cache.set(String(catId), categoryData.category.cat_id)
        }
        return categoryData.category.cat_id
      }
      currentCatId = categoryData.category.cat_rootcat
    }
  }

  private async groupProductsByRootCategory(products: any[], groupByRoot: boolean = true): Promise<{ productsByCategory: Record<string, any[]>; categoryOrder: string[] }> {
    //console.time('groupProductsByRootCategory')
    // If no products, return empty object
    if (!products || !products.length) {
      return { productsByCategory: {}, categoryOrder: [] }
    }

    // Create a copy of products array and add index for tracking original order
    const productsWithIndex = products.map((product, index) => ({
      ...product,
      _originalOrder: index
    }))

    if (!groupByRoot) {
      // Оптимизированная группировка по категориям
      const result: Record<string, any[]> = {}
      const categoryMap: Record<string, any[]> = {}
      const categoryOrder: string[] = []

      // Группируем товары по категориям за один проход
      for (const product of productsWithIndex) {
        const catId = product.prod_cat
        if (catId) {
          if (!categoryMap[catId]) {
            categoryMap[catId] = []
            categoryOrder.push(catId)
          }
          categoryMap[catId].push(product)
        }
      }

      // Сортируем товары в каждой категории и удаляем временное поле
      for (const catId of categoryOrder) {
        result[catId] = categoryMap[catId].sort((a, b) => a._originalOrder - b._originalOrder).map(({ _originalOrder, ...product }) => product)
      }

      //console.timeEnd('groupProductsByRootCategory')
      return { productsByCategory: result, categoryOrder }
    }

    // Оптимизированная группировка по корневым категориям
    //console.time('make categoryOrder')
    const catIds = Array.from(new Set(productsWithIndex.map((p) => p.prod_cat)))

    // Создаем локальный кэш для этого вызова метода
    const rootCategoryCache = new Map<string, number>()

    // Получаем корневые категории параллельно
    await Promise.all(
      catIds.map(async (catId) => {
        // Используем общий кэш для всех вызовов getRootCategoryId
        const rootId = await this.getRootCategoryId(catId, rootCategoryCache)
        rootCategoryCache.set(String(catId), rootId)
      })
    )

    // Оптимизированное создание структур данных для группировки
    const rootCategoryMap: Record<number, any[]> = {}
    const rootCategoryOrder: string[] = []
    const processedRootCatIds = new Set<string>()

    // Группируем товары по корневым категориям за один проход
    for (const product of productsWithIndex) {
      const rootCatId = rootCategoryCache.get(String(product.prod_cat))
      if (rootCatId) {
        const rootCatIdStr = String(rootCatId)
        // Добавляем категорию в порядок, если её ещё нет
        if (!processedRootCatIds.has(rootCatIdStr)) {
          processedRootCatIds.add(rootCatIdStr)
          rootCategoryOrder.push(rootCatIdStr)
        }

        // Добавляем товар в соответствующую категорию
        if (!rootCategoryMap[rootCatId]) {
          rootCategoryMap[rootCatId] = []
        }
        rootCategoryMap[rootCatId].push(product)
      }
    }
    //console.timeEnd('make categoryOrder')

    // Создаем результат, сохраняя порядок категорий и товаров
    const result: Record<string, any[]> = {}

    // Добавляем категории в результат в порядке их появления
    for (const rootCatId of rootCategoryOrder) {
      const numericRootCatId = Number(rootCatId)
      if (rootCategoryMap[numericRootCatId]) {
        // Сортируем товары по исходному порядку и удаляем временное поле
        result[rootCatId] = rootCategoryMap[numericRootCatId].sort((a, b) => a._originalOrder - b._originalOrder).map(({ _originalOrder, ...product }) => product)
      }
    }

    //console.timeEnd('groupProductsByRootCategory')
    return { productsByCategory: result, categoryOrder: rootCategoryOrder }
  }

  private async fetchMeiliCategoryData(catId: string | number, { withAnalogColumn = false } = {}): Promise<{ columns: any; filters: any; category: any }> {
    // Сначала ищем только по catId
    const [columnsByCat, filtersByCat, categorySearch] = await Promise.all([
      this.meiliDB.index('columns').search('', {
        filter: [`cat_id = ${catId}`],
        sort: ['sort:asc'],
        distinct: 'keyname',
        limit: 900
      }),
      this.meiliDB.index('filters').search('', {
        filter: [`category_id = ${catId}`]
      }),
      this.meiliDB.index('categories').search('', {
        filter: [`cat_id = ${catId}`],
        limit: 1
      })
    ])

    // Если ничего не найдено по catId, ищем по GLOBAL_CATEGORY_ID
    let columns = columnsByCat.hits
    if (!columns.length) {
      const globalColumns = await this.meiliDB.index('columns').search('', {
        filter: [`cat_id = ${GLOBAL_CATEGORY_ID}`],
        sort: ['sort:asc'],
        distinct: 'keyname',
        limit: 900
      })
      columns = globalColumns.hits
    }

    let filters = filtersByCat.hits
    if (!filters.length) {
      const globalFilters = await this.meiliDB.index('filters').search('', {
        filter: [`category_id = ${GLOBAL_CATEGORY_ID}`]
      })
      filters = globalFilters.hits
    }

    if (withAnalogColumn && columns.findIndex((x) => x.keyname === 'prod_analogsku') === -1) {
      const skuColIndex = columns.findIndex((x) => x.keyname === 'prod_sku')
      if (skuColIndex > -1) {
        columns.splice(skuColIndex + 1, 0, {
          keyname: 'prod_analogsku',
          title: 'Аналог',
          sort: 0,
          sorted: 1
        })
      }
    }

    return {
      columns,
      filters,
      category: categorySearch.hits[0]
    }
  }

  public processSearchQuery(query: string): string {
    const dotregexp = /\,{1,}/gm
    const processedQuery = String(query)
      .replace(dotregexp, '.')
      .split(' ')
      .filter((item) => !isSize(item))
      .join(' ')

    return processedQuery
  }

  private buildMeiliFilter(rawFilters: any): { filterConditions: { [key: string]: any }; filter: string[] } {
    let filterConditions: { [key: string]: any } = {}
    if (rawFilters) {
      const parsedFilters = this.parseFilters(rawFilters)
      if (parsedFilters) {
        Object.keys(parsedFilters).forEach((key) => {
          if (parsedFilters[key]?.length) {
            filterConditions[key] = parsedFilters[key]
          }
        })
      }
    }

    const filter = Object.entries(filterConditions).map(([key, values]) => {
      return `${key} IN [${values.map((value: string) => `"${value}"`).join(',')}]`
    })

    return { filterConditions, filter }
  }

  public getMeiliSizeSort(direction: 'asc' | 'desc', { ignoreSlashSize = false } = {}): string[] {
    const sizes = [`size_in:${direction}`, `size_in_2:${direction}`, `size_out:${direction}`, `size_out_2:${direction}`, `size_h:${direction}`, `size_h_2:${direction}`]

    if (ignoreSlashSize) {
      return sizes.filter((size) => !size.includes('_2:'))
    }

    return sizes
  }

  public buildMeiliSortConditions(sorting: Array<{ column: string; direction: 'asc' | 'desc'; order?: number }>, { ignoreSlashSize = false } = {}): string[] {
    //console.log('🚀 ~ ProductProvider ~ buildMeiliSortConditions ~ sorting:', sorting)

    // Создаем массив для результата
    const result: string[] = []

    // Если сортировка пуста, возвращаем пустой массив
    if (!sorting || sorting.length === 0) {
      //console.log('🚀 ~ ProductProvider ~ buildMeiliSortConditions ~ empty sorting, returning:', result)
      return result
    }

    // Добавляем сортировки
    const otherSorts = sorting.flatMap((sortItem) => {
      // Если это сортировка по размеру, используем специальный метод
      if (sortItem.column === 'prod_size') {
        const sizeSorts = this.getMeiliSizeSort(sortItem.direction, { ignoreSlashSize })
        //console.log('🚀 ~ ProductProvider ~ buildMeiliSortConditions ~ sizeSorts:', sizeSorts)
        return sizeSorts
      }
      // Если это сортировка по категории, пропускаем
      if (sortItem.column === 'category' || sortItem.column === 'cat_search_sort') {
        return [] // Возвращаем пустой массив, чтобы не дублировать сортировку
      }
      // Для всех остальных полей используем стандартный формат
      return `${sortItem.column}:${sortItem.direction}`
    })

    // Объединяем сортировку по категории и остальные сортировки
    const finalResult = [...result, ...otherSorts]
    //console.log('🚀 ~ ProductProvider ~ buildMeiliSortConditions ~ finalResult:', finalResult)
    return finalResult
  }

  replaceAnalogSkuWithFirstAnalog(products: Partial<products>[], searchvalue: string): Partial<products>[] {
    if (!products || !products.length || !searchvalue) {
      return products
    }

    return products.map((product) => {
      // Создаем копию товара, чтобы не изменять оригинальный объект
      const updatedProduct = { ...product }

      // Проверяем наличие поля prod_analogs и что оно не пустое
      if (updatedProduct.prod_analogs && typeof updatedProduct.prod_analogs === 'string') {
        const analogs = updatedProduct.prod_analogs
          .split(',')
          .map((analog) => analog.trim())
          .filter(Boolean)

        // Если есть хотя бы один аналог
        if (analogs.length > 0) {
          const firstAnalog = analogs[0]

          // Если передан searchvalue, дополнительно проверяем совпадение с ним
          if (searchvalue) {
            const normalizedSearchValue = searchvalue.trim().toLowerCase()

            // Проверяем prod_analogsku
            if (updatedProduct.prod_analogsku && updatedProduct.prod_analogsku.toLowerCase() === normalizedSearchValue) {
              updatedProduct.prod_sku = firstAnalog
            }
            // Проверяем prod_sku
            else if (updatedProduct.prod_sku && updatedProduct.prod_sku.toLowerCase() === normalizedSearchValue) {
              updatedProduct.prod_sku = firstAnalog
            }

            // Проверка на наличие searchvalue в аналогах
            const matchingAnalog = analogs.find((analog) => analog.trim().toLowerCase() === normalizedSearchValue)
            if (matchingAnalog) {
              updatedProduct.prod_sku = firstAnalog
            }
          }
          // // Всегда проверяем идентичность prod_sku и prod_analogsku
          // if (updatedProduct.prod_sku && updatedProduct.prod_analogsku && updatedProduct.prod_sku === updatedProduct.prod_analogsku) {
          //   // Если поля идентичны, заменяем prod_sku на первый аналог
          //   updatedProduct.prod_analogsku = firstAnalog
          // }
        }
      }

      return updatedProduct
    })
  }
}

export const productProvider = new ProductProvider()
