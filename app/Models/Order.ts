import { DateTime } from 'luxon'
import { afterSave, BaseModel, beforeSave, BelongsTo, belongsTo, column, hasMany, HasMany, hasOne, HasOne } from '@ioc:Adonis/Lucid/Orm'
import OrderItem from 'App/Models/OrderItem'
import Client from 'App/Models/Client'
import Mail from '@ioc:Adonis/Addons/Mail'

//import { shippingInterface } from 'App/Interfaces/orders/shippingInterface';
import { SberbankApi } from 'App/Helpers/sberbank'
import Env from '@ioc:Adonis/Core/Env'
import OrderSnapshot from 'App/Models/OrderSnapshot'
import { PochtaRU } from 'App/Plugins/PochtaRU'
import Lock from 'App/Models/Lock'
import Product from 'App/Models/Product'
import Database from '@ioc:Adonis/Lucid/Database'
import View from '@ioc:Adonis/Core/View'
import { SnapshotBodyInterface } from 'App/Interfaces/orders/snapshotBody'
import Gtd from 'App/Plugins/Gtd'
import { GtdSpecData } from 'App/Interfaces/orders/GtdSpecData'
import Passport from 'App/Models/Passport'
import loadSettings from 'App/Helpers/loadSettings'

const EMAIL_FROM = Env.get('EMAIL_FROM')

//TODO: move to db
const discountStartSum = Number(Env.get('DISCOUNT_START_SUM')) //|| 7500
const bigDiscountStartSum = Number(Env.get('BIG_DISCOUNT_START_SUM')) //|| 100000
const bigDiscountValue = Number(Env.get('BIG_DISCOUNT_VALUE')) //|| 10

interface GetOrderPriceResult {
  sum: number
  discountValue: number
  whosalePrices: boolean
  defSum: number
  bigDiscount: boolean
}

const groupBy = (items, key) => {
  return items.reduce(
    (result, item) => ({
      ...result,
      [item[key]]: [...(result[item[key]] || []), item]
    }),
    {}
  )
}

export default class Order extends BaseModel {
  @column({ isPrimary: true })
  public order_id: number

  @column.dateTime({
    autoCreate: true
    // serialize: (value?: DateTime) => {
    //   try {
    //     return value ? value.toFormat('dd.LL.yy TT') : value
    //   } catch (error) {
    //     return value
    //   }
    // }
  })
  public order_datetime: DateTime

  /*   @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime */

  @column()
  public order_status: string

  @column()
  public order_client: number

  @column()
  public order_shipping: string

  @column()
  public order_payment: string

  @column()
  public order_price: number

  @column()
  public order_shippingprice: number

  @column()
  public order_desc: string

  @column()
  public order_clienttype: string

  @column()
  public order_locale: string

  @column()
  public order_coupons: string

  @column()
  public order_notice: string

  @column()
  public order_gtd: boolean

  @column()
  public order_tracknumber: string

  @column()
  public order_weight: number

  @column()
  public order_lastupdate_person: string

  @hasMany(() => OrderItem, {
    foreignKey: 'items_order_id',
    localKey: 'order_id'
  })
  public items: HasMany<typeof OrderItem>

  @hasMany(() => OrderSnapshot, {
    foreignKey: 'orderid',
    localKey: 'order_id',
    onQuery: (query) => query.orderBy('ID', 'desc')
  })
  public snapshots: HasMany<typeof OrderSnapshot>

  @hasOne(() => OrderSnapshot, {
    foreignKey: 'orderid',
    localKey: 'order_id',
    onQuery: (query) => query.orderBy('ID', 'desc') //.first()
  })
  public lastSnapshot: HasOne<typeof OrderSnapshot>

  @hasOne(() => Client, {
    foreignKey: 'client_id',
    localKey: 'order_client'
  })
  public client: HasOne<typeof Client>

  @belongsTo(() => Client, {
    localKey: 'client_id',
    foreignKey: 'order_client'
  })
  public cclient: BelongsTo<typeof Client>

  @hasOne(() => Lock, {
    localKey: 'order_id',
    foreignKey: 'entity_id',
    onQuery(query) {
      query.select('active', 'created_at', 'user_name').where({ entity: 'orders', active: true })
    }
  })
  public lock: HasOne<typeof Lock>

  @beforeSave()
  public static async beforeSaveHook(order: Order) {
    try {
      if (order.order_status == 'Резерв') {
        const reservedOrders = await Order.query().where('order_status', 'Резерв').andWhere('order_client', order.order_client)

        if (reservedOrders.length) {
          const regex = /##(.*?)##/
          const msg = `##другие резервы: ${reservedOrders.map((order) => order.order_id).join(', ')}##`

          if (order.order_notice.match(regex)) {
            order.order_notice = order.order_notice.replace(regex, msg)
          } else {
            order.order_notice += msg
          }
        }
      }
    } catch (error) {
      console.log(error)
    }

    // console.log('before save order hook: ', {
    //   order_locale: order.order_locale,
    //   order_tracknumber: order.order_tracknumber,
    //   dirty_track: order.$dirty['order_tracknumber']
    // })

    try {
      if (order.order_locale === 'ru' && order.order_tracknumber && order.$dirty.order_tracknumber) {
        let msgs_ru = ''

        if (order.order_shipping === 'СДЭК') {
          msgs_ru = `<p>Здравствуйте!</p>
                    <p>Вашему заказу присвоен трек-номер: ${order.order_tracknumber}</p>
                    <p><span style="color:rgb(255,0,0);font-size:20px;"><strong>Внимание!</strong></span><span style="color:rgb(26,26,26);font-size:16px;"><strong>&nbsp;</strong> После прохождения таможенного оформления (3-5 дней), статус заказа начнёт меняться.</span></p>
                    <p><span style="color:rgb(26,26,26);font-size:16px;">Отследить заказ Вы можете по ссылке: </span><a target="_blank" rel="noopener noreferrer" href="https://www.cdek.ru/ru/tracking/?order_id=${order.order_tracknumber}"><span style="color:rgb(26,26,26);font-size:16px;">https://www.cdek.ru/ru/tracking/?order_id=${order.order_tracknumber}</span></a></p>`
        } else {
          msgs_ru = `<p>Здравствуйте!</p>
                      <p>Вашему заказу присвоен трек-номер: ${order.order_tracknumber}</p>
                      <p><span style="color:rgb(26,26,26);font-size:16px;">Отследить заказ Вы можете по ссылке:</span> &nbsp;<a target="_blank" rel="noopener noreferrer" href="https://www.pochta.ru/tracking#${order.order_tracknumber}">https://www.pochta.ru/tracking#${order.order_tracknumber}</a></p>`
        }

        const orderLastSnapshot = await Order.lastSnapshot(order.order_id)
        const orderState: SnapshotBodyInterface = orderLastSnapshot?.body

        Mail.send((message) => {
          message
            //format
            .from(Env.get('EMAIL_FROM'), Env.get('EMAIL_FROM_NAME'))
            .to(orderState.client.client_mail)
            .subject(`${Env.get('EMAIL_FROM_NAME')}: Заказ #${order.order_id} - трек-номер.`)
            .html(msgs_ru)
        })
      }
    } catch (error) {
      console.error(error)
    }

    // console.log('before save order hook: ', {
    //   order_locale: order.order_locale,
    //   order_status: order.order_status,
    //   dirty_status: order.$dirty['order_status']
    // })

    try {
      if (order.order_locale == 'ru' && order.order_status == 'Оплачен' && order.$dirty['order_status']) {
        const orderLastSnapshot = await Order.lastSnapshot(order.order_id)
        const orderState: SnapshotBodyInterface = orderLastSnapshot?.body

        let msgs_ru = `${orderState.client.client_name}, Здравствуйте! Оплату #${order.order_id} получили, сегодня Ваш заказ будет передан в службу доставки. В течение трех-семи рабочих дней Вам будет выслан трекинг номер для отслеживания передвижения посылки.`

        Mail.send((message) => {
          message
            //format
            .from(Env.get('EMAIL_FROM'), Env.get('EMAIL_FROM_NAME'))
            .to(orderState.client.client_mail)
            .subject(`${Env.get('EMAIL_FROM_NAME') }: Подтверждение оплаты заказа #${order.order_id}`)
            .html(msgs_ru)
        })
      }
    } catch (error) {
      console.error(error)
    }

    if (typeof order.order_coupons !== 'string') {
      order.order_coupons = JSON.stringify(order.order_coupons)
    }
  }

  public async isLock() {
    return await Lock.isLock({
      entity: 'orders',
      entity_id: this.order_id
    })
  }

  static async lastSnapshot(id) {
    return await OrderSnapshot.query().where('orderid', id).orderBy('ID', 'desc').first()
  }

  static async checkFullGtd(orderId: number) {
    const gtd = new Gtd()

    return await gtd.checkFull(orderId)
  }

  async _checkFullGtd() {
    const gtd = new Gtd()
    const res = await gtd.checkFull(this.order_id)

    this.fullGtd = res
    return res
  }

  static async cpanOrderByID(id: number) {
    const gtd = new Gtd()
    // try {
    //     Journal.createItem({
    //         entity: "orders",
    //         entity_id: id,
    //         msg: "просмотр заказа",
    //     });
    // } catch (error) {}

    const _order = await Order.query()
      .where('order_id', id)
      .preload('snapshots', (query) => query.orderBy('ID', 'desc'))
      .first()

    // await order.load('client')
    // await order.load("items")
    // const snapshots = await _order.load("snapshots")

    if (!_order) {
      return null
    }

    // const data =  //snapshots?.length ? snapshots[0] : _order //Order.lastSnapshot(id)
    // const meta = await _order.getPrice()

    const orderProducts = await OrderItem.query().where('items_order_id', _order.order_id).preload('product')

    let data: SnapshotBodyInterface = _order?.snapshots[0].body as SnapshotBodyInterface

    data?.items?.map((item) => {
      item.currentState = orderProducts.find((x) => x?.product?.prod_id == (item?.prod_id || item?.item_id))?.product || {}
    })

    const clientPassport = await Passport.findBy('client_id', data.client.client_id)

    if (clientPassport?.body) {
      data.client.passport = clientPassport //.body
    }

    if (_order.order_gtd) {
      let specsColumns = [
        'spec_journal.spec_id',
        'spec_journal.date',
        'spec_journal.orderId',
        'spec_journal.prod',
        'spec_journal.qty',
        'spec_journal.increment',
        'specs.spec_num',
        'specs.spec_title',
        'specs.spec_date',
        'specs.filepath',
        'spec_list.spec',
        'spec_list.qty as balance',
        'spec_list.invoice',
        'spec_list.np',
        'products.prod_size',
        'products.prod_manuf',
        'products.prod_weight',
        'products.prod_purpose',
        'products.prod_type',
        'products.prod_sku',
        'products.prod_analogsku'
      ]

      let Specs: GtdSpecData[] = []

      await Promise.all(
        data.items.map(async (item) => {
          const _specs = await Database.from('spec_journal')
            .select(specsColumns)
            .leftJoin('products', 'spec_journal.prod', 'products.prod_analogsku')
            .leftJoin('spec_list', function () {
              this.on('spec_journal.prod', '=', 'spec_list.prod').andOn('spec_journal.spec_id', '=', 'spec_list.spec')
            })
            .leftOuterJoin('specs', 'spec_journal.spec_id', 'specs.id')
            .where({ orderId: _order.order_id, 'spec_journal.prod': item.prod_analogsku, increment: 0, transit: 0 })
            .groupBy('specs.id')
          //.debug()
          if (_specs.length > 0) {
            item.gtdreserve = _specs.reduce((a, b) => a + b.qty, 0)
            item.specs = _specs

            Specs = _specs.concat(Specs)
          }
        })
      )

      data.specs = groupBy(Specs, 'spec_id')

      for (const key in data.specs) {
        let _totalweight

        data.specs[key].map((i) => {
          if (!i.prod_weight) i.prod_weight = 11

          i.summweight = Number(i.prod_weight) * i.qty

          _totalweight += i.summweight
        })

        //console.log('_totalweight', _totalweight)
        //order.specs[key].totalweight = _totalweight
      }

      // console.log('🚀 ~ Order ~ cpanOrderByID ~ data.specs:', data.specs)

      data.items.forEach((item) => {
        item.notExported = !Specs.find((x) => x.prod == item.prod_analogsku)
      })
    }

    if (_order.snapshots?.length == 0) {
      const client = await Client.query().where('client_id', _order.order_client).preload('org')

      let _orderCopy = Object.assign({}, _order)
      _orderCopy.client = client
      _orderCopy.items = orderProducts

      data = _orderCopy

      _order.snapshots.push({
        ID: 0,
        orderid: order.order_id,
        date: DateTime.now(), //'0000-00-00 00:00:00',
        body: _orderCopy, //JSON.stringify(_orderCopy),
        user: 'client'
      })
    }

    return {
      data,
      snapshots: _order.snapshots
    }
  }

  async fillData(orderData, client, orderSum, LOCALE) {
    this.order_status = 'Не обработан'
    this.order_client = client?.client_id || 0
    this.order_shipping = orderData?.shipping?.shortName || orderData?.shipping?.methodName || 'Не указано'
    this.order_shippingprice = orderData?.shipping?.price || 301
    this.order_payment = orderData?.payment?.method || 'Не указано'
    this.order_price = orderSum
    //this.order_clienttype = ''
    this.order_locale = LOCALE
    this.order_coupons = JSON.stringify({})
    this.order_desc = orderData?.notice || ''

    try {
      if (this.order_shippingprice == 301) {
        const pochta = new PochtaRU()
        this.order_shippingprice = await pochta.manualCalc({ orderprice: orderSum, countryID: '112' })
      }
    } catch (error) {}
  }

  static async getWeight(id: number) {
    if (!id) {
      return 0
    }
    const orderItems = await OrderItem.query().where('items_order_id', id).preload('product')

    return await Product.getProductsWeight(orderItems.map((item) => ({ qty: item.item_count, product: item.product })))
  }

  async getPrice() {
    return await Order.getOrderPrice(this.order_id)
  }

  static async checkList({ toEmail = false, isRumi = false }) {
    const [paid, paused, ignored, reserved] = await Promise.all([
      await Order.query()
        .whereIn('order_status', ['Оплачен'])
        .andWhere('order_tracknumber', '')
        .andWhere((query) => {
          !isRumi ? query.andWhere('order_locale', '=', 'ru') : query.andWhere('order_locale', '!=', 'ru')
        })
        .andWhereRaw('order_datetime < DATE_SUB(CURDATE(), INTERVAL 3 DAY)')
        // .andWhereRaw('order_datetime >= DATE_SUB(CURDATE(), INTERVAL 2 MONTH)'),
        .andWhereRaw('YEAR(order_datetime) >= YEAR(NOW())')
        // .preload('client')
        .orderBy('order_datetime', 'desc'),

      await Order.query()
        .whereIn('order_status', ['В ожидании оплаты', 'В работе'])
        .andWhereRaw('order_datetime < DATE_SUB(CURDATE(), INTERVAL 6 DAY)')
        .andWhere((query) => {
          !isRumi ? query.andWhere('order_locale', '=', 'ru') : query.andWhere('order_locale', '!=', 'ru')
        })
        // .andWhereRaw('order_datetime >= DATE_SUB(CURDATE(), INTERVAL 2 MONTH)'),
        .andWhereRaw('YEAR(order_datetime) >= YEAR(NOW())')
        // .preload('client')
        .orderBy('order_datetime', 'desc'),
      await Order.query()
        .whereIn('order_status', ['Не обработан'])
        .andWhereRaw('order_datetime < DATE_SUB(CURDATE(), INTERVAL 2 DAY)')
        .andWhere((query) => {
          !isRumi ? query.andWhere('order_locale', '=', 'ru') : query.andWhere('order_locale', '!=', 'ru')
        })
        // .andWhereRaw('order_datetime >= DATE_SUB(CURDATE(), INTERVAL 2 MONTH)')
        .andWhereRaw('YEAR(order_datetime) >= YEAR(NOW())')
        // .preload('client')
        .orderBy('order_datetime', 'desc'),

      await Order.query()
        .whereIn('order_status', ['Резерв'])
        .andWhere('order_tracknumber', '')
        .andWhere((query) => {
          !isRumi ? query.andWhere('order_locale', '=', 'ru') : query.andWhere('order_locale', '!=', 'ru')
        })
        .andWhereRaw('order_datetime < DATE_SUB(CURDATE(), INTERVAL 30 DAY)')
        .orderBy('order_datetime', 'desc')
    ])

    if (toEmail) {
      //TODO: move this template to db
      const htmlBody = View.renderRawSync(
        `
        <h3> Заказы требующие внимания: </h3>
        <hr />
        <p><b>"Не обработан"</b> более 2 дней: </p>
        @each(order in ignored)
          <li> 
            <a href="https://dev.rti-baltika.ru/orders/{{order.order_id}}" target="_blank">#{{order.order_id}} от {{order.order_datetime?.toLocaleString()}}</a>
          </li>
        @end
        <hr/>
        <p><b>"Оплачен"</b>, без треков более 3 дней: </p>
        @each(order in paid)
          <li> 
            <a href="https://dev.rti-baltika.ru/orders/{{order.order_id}}" target="_blank">#{{order.order_id}} от {{order.order_datetime?.toLocaleString()}}</a>
          </li>
        @end
        <hr />
        <p><b>"В ожидании оплаты"</b> более 6 дней:</p>
        @each(order in paused)
          <li> 
            <a href="https://dev.rti-baltika.ru/orders/{{order.order_id}}" target="_blank">#{{order.order_id}} от {{order.order_datetime?.toLocaleString()}}</a>
          </li>
        @end
        <hr />
        <p><b>"В резерве"</b> более 30 дней:</p>
        @each(order in reserved)
          <li> 
            <a href="https://dev.rti-baltika.ru/orders/{{order.order_id}}" target="_blank">#{{order.order_id}} от {{order.order_datetime?.toLocaleString()}}</a>
          </li>
        @end
      `,
        {
          paid,
          paused,
          ignored,
          reserved
        }
      )

      Mail.use('smtp').send((message) => {
        message
          .from(Env.get('EMAIL_FROM'))
          .to(isRumi ? Env.get('RUMI_SMTP_FROM', '<EMAIL>') : Env.get('EMAIL_FROM'))
          .subject((isRumi ? 'Rumisota: ' : 'Мир Сальников: ') + 'заказы требующие внимания')
          .html(htmlBody)
      })

      return htmlBody
    }

    return {
      paid,
      paused,
      ignored,
      reserved
    }
  }

  static async getOrderPrice(id: number): Promise<GetOrderPriceResult> {
    // TODO: get data from snapshot
    const q = `select sum( order_items.item_count *
                  IF( (select sum(order_items.item_count * products.prod_price) as orderSum
                  from order_items
                  left outer join products
                  on products.prod_id = order_items.item_id
                  where order_items.items_order_id = ? limit ?) > ${discountStartSum},
                  products.prod_price - ((products.prod_price / 100) * products.prod_discount), products.prod_price ) ) as orderSum,
                  sum(order_items.item_count * products.prod_price) as defSum
                  from order_items
                  left outer join products
                  on products.prod_id = order_items.item_id
                  where order_items.items_order_id = ? limit ?`
    let [[{ orderSum, defSum }]]: any = await Database.rawQuery(q, [id, 1, id, 1])
    let discountValue = 0
    let whosalePrices = defSum > discountStartSum
    let bigDiscount = orderSum > bigDiscountStartSum
    if (orderSum > bigDiscountStartSum) {
      discountValue = (orderSum / 100) * bigDiscountValue
      orderSum -= discountValue
    }
    return {
      sum: orderSum ? Number(orderSum.toFixed(2)) : 0,
      discountValue,
      whosalePrices,
      defSum,
      bigDiscount
    }
  }
  static async beta_getOrderPrice(id: number): Promise<GetOrderPriceResult> {
    const order = await Order.findOrFail(id)
    console.log('🚀 ~ Order ~ getOrderPrice ~ order:', order.order_price)
    //await this.order.load('client', query => query.preload('org'))
    await order.load('snapshots', (query) => query.orderBy('ID', 'desc'))

    let snapshot: SnapshotBodyInterface = order.snapshots[0]?.body

    if (!snapshot) {
      throw new Error('Order not found')
    }

    // const settingsData = {}

    let settingsData = await loadSettings(['DISCOUNT_START_SUM', 'BIG_DISCOUNT_START_SUM', 'BIG_DISCOUNT_VALUE'])

    if (!settingsData) {
      throw new Error('Settings not found')
    }
    const orderItems = snapshot.items

    orderItems.map((item) => {
      if (typeof item.orderCount == 'undefined') {
        item.orderCount = item.item_count || item.qty
      }

      if (typeof item.whosaleprice != 'number') {
        item.whosaleprice = item.prod_price - (item.prod_price / 100) * (item.discount || 10)
      }
    })

    // console.log('🚀 ~ Order ~ orderItems.map ~ orderItems:', orderItems)

    let defSum = orderItems.map((item) => item.orderCount * item.prod_price).reduce((acc, val) => acc + val, 0)
    const wholesalePrices = defSum > settingsData.DISCOUNT_START_SUM

    let itemsSum = orderItems.map((item) => item.orderCount * (wholesalePrices ? item.whosaleprice : item.prod_price)).reduce((acc, val) => acc + val, 0)
    console.log('🚀 ~ Order ~ getOrderPrice ~ itemsSum:', itemsSum)

    let orderSum = itemsSum - itemsSum * ((snapshot.order_coupons.personal || 0) / 100)

    console.log('🚀 ~ Order ~ getOrderPrice ~ orderSum:', orderSum)
    console.log('🚀 ~ Order ~ getOrderPrice ~ snapshot.order_coupons.personal:', snapshot.order_coupons.personal)

    if (itemsSum > settingsData?.BIG_DISCOUNT_START_SUM) {
      if (settingsData.BIG_DISCOUNT_VALUE > snapshot.order_coupons.personal) {
        orderSum = itemsSum - itemsSum * ((settingsData.BIG_DISCOUNT_VALUE || 10) / 100)
      }
    }

    const discount = (itemsSum / 100) * Number(snapshot.order_coupons.personal)

    let bigDiscountValue = 0

    if (orderSum > settingsData.BIG_DISCOUNT_START_SUM) {
      bigDiscountValue = (orderSum / 100) * settingsData.BIG_DISCOUNT_VALUE
      orderSum -= bigDiscountValue
    }

    return {
      bigDiscount: itemsSum > settingsData.BIG_DISCOUNT_START_SUM,
      defSum,
      bigDiscountValue,
      discount,
      sum: Number(orderSum.toFixed(2)),
      whosalePrices: wholesalePrices,
      total: orderSum + snapshot.order_shippingprice - discount
    }
  }

  async orderWeight() {
    return await Order.getWeight(this.order_id)
  }

  async sberRegOrder({ cartItems = undefined, client = undefined, shipping = undefined }) {
    const sberbankApi = new SberbankApi()

    if (!client) {
    }

    if (!cartItems) {
    }

    let sberData = await sberbankApi.regOrder({
      orderNumber: this.order_id,
      amount: Number(this.order_price) + Number(this.order_shippingprice),
      cartItems,
      client,
      returnUrl: '',
      shipping
    })

    try {
      sberData = JSON.parse(sberData)
    } catch (error) {
      console.error('Order.ts:: sberRegOrder: ', error)
    }

    if (sberData.formUrl) {
      try {
        Mail.send((message) => {
          message
            .from(EMAIL_FROM, Env.get('EMAIL_FROM_NAME'))
            .to(client.client_mail)
            .subject('Оплата заказа #' + this.order_id)
            .htmlView('mail_sberpay', { order_id: this.order_id, formUrl: sberData.formUrl })
        })
      } catch (error) {
        console.error(error)
      }
    } else {
      let m_text = sberData.errorMessage

      try {
        console.error(`Эквайринг. Ошибка генерации формы оплаты, клиент: ${client?.client_name} / (${client?.client_mail})` + m_text)

        /*         Mail.send((message) => {
          message
            .from(EMAIL_FROM)
            .to(EMAIL_FROM)
            .subject(`Эквайринг. Ошибка генерации формы оплаты, клиент: ${client?.client_name} / (${client?.client_mail})`)
            .text(m_text)
          //.html(m_text)
        }) */
      } catch (error) {
        console.error(error)
      }
    }

    return sberData
  }
}
